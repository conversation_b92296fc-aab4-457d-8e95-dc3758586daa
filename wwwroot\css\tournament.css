/* Cricket Tournament Custom Styles */

:root {
    --cricket-green: #2d5016;
    --cricket-brown: #8b4513;
    --cricket-gold: #ffd700;
    --cricket-white: #ffffff;
    --cricket-red: #dc3545;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--cricket-green) 0%, #1a3009 100%);
    min-height: 70vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
    opacity: 0.3;
}

.cricket-ball-animation {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

/* Legends Section */
.legends-section {
    background: #f8f9fa;
}

.section-title {
    color: var(--cricket-green);
    font-weight: bold;
    margin-bottom: 1rem;
}

.section-subtitle {
    color: #6c757d;
    font-size: 1.1rem;
}

.legend-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.legend-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.legend-image img {
    border: 5px solid var(--cricket-gold);
    transition: transform 0.3s ease;
}

.legend-card:hover .legend-image img {
    transform: scale(1.05);
}

.legend-quote {
    font-style: italic;
    color: #6c757d;
    border-left: 4px solid var(--cricket-gold);
    padding-left: 1rem;
    margin-top: 1rem;
}

/* Info Cards */
.info-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.info-card:hover {
    transform: translateY(-5px);
}

.info-icon {
    margin-bottom: 1rem;
}

/* Registration Form */
.registration-card {
    background: white;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    overflow: hidden;
}

.registration-card .card-header {
    background: linear-gradient(135deg, var(--cricket-green), #2d5016);
    color: white;
    padding: 2rem;
    border: none;
}

.registration-card .card-body {
    padding: 2rem;
}

.form-control, .form-select {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--cricket-green);
    box-shadow: 0 0 0 0.2rem rgba(45, 80, 22, 0.25);
}

.tournament-rules {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid var(--cricket-green);
}

/* Buttons */
.btn-primary {
    background: linear-gradient(135deg, var(--cricket-green), #2d5016);
    border: none;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #1a3009, var(--cricket-green));
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(45, 80, 22, 0.3);
}

.btn-outline-light {
    border: 2px solid white;
    border-radius: 25px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background: white;
    color: var(--cricket-green);
    transform: translateY(-2px);
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--cricket-green) 0%, #1a3009 100%);
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        min-height: 60vh;
        text-align: center;
        padding: 3rem 0;
    }

    .hero-section h1 {
        font-size: 2.5rem;
    }

    .hero-section .lead {
        font-size: 1.1rem;
    }

    .legend-card {
        margin-bottom: 2rem;
    }

    .legend-card img {
        width: 120px;
        height: 120px;
    }

    .info-card {
        margin-bottom: 1.5rem;
    }

    .info-card h3 {
        font-size: 1.5rem;
    }

    .hero-buttons .btn {
        display: block;
        width: 100%;
        margin-bottom: 1rem;
    }

    .navbar-nav {
        text-align: center;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem;
    }

    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }

    .display-4 {
        font-size: 2rem;
    }

    .container {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .team-card, .highlight-card {
        margin-bottom: 1.5rem;
    }

    .countdown-timer {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .countdown-item {
        min-width: 100px;
    }

    .countdown-number {
        font-size: 2.5rem;
    }
}

@media (max-width: 576px) {
    .hero-section h1 {
        font-size: 2rem;
    }

    .legend-card {
        text-align: center;
        padding: 1.5rem;
    }

    .legend-card img {
        width: 100px;
        height: 100px;
    }

    .info-card h3 {
        font-size: 1.25rem;
    }

    .info-card .display-6 {
        font-size: 1.5rem;
    }

    .navbar-brand {
        font-size: 1.1rem;
    }

    .btn {
        margin-bottom: 0.5rem;
    }

    .countdown-number {
        font-size: 2rem;
    }

    .stat-number {
        font-size: 2rem;
    }

    .step-circle {
        width: 40px;
        height: 40px;
    }
}

/* Animation for page load */
.fade-in {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.6s ease-out;
}

.animate-on-scroll.animate-in {
    opacity: 1;
    transform: translateY(0);
}

/* Staggered animation for multiple elements */
.animate-on-scroll:nth-child(1) { transition-delay: 0.1s; }
.animate-on-scroll:nth-child(2) { transition-delay: 0.2s; }
.animate-on-scroll:nth-child(3) { transition-delay: 0.3s; }
.animate-on-scroll:nth-child(4) { transition-delay: 0.4s; }

/* Enhanced hover effects */
.legend-card, .info-card, .highlight-card, .team-card, .stat-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.legend-card:hover, .info-card:hover, .highlight-card:hover, .team-card:hover, .stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Form validation styles */
.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

/* Cricket-themed decorations */
.cricket-decoration {
    position: relative;
}

.cricket-decoration::before {
    content: '🏏';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 1.5rem;
    opacity: 0.7;
}

/* Success page styles */
.success-icon {
    animation: successPulse 2s infinite;
}

@keyframes successPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

/* Table styles */
.table-hover tbody tr:hover {
    background-color: rgba(45, 80, 22, 0.05);
}

/* Badge styles */
.badge {
    border-radius: 15px;
    padding: 0.5rem 1rem;
    font-weight: 500;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--cricket-green);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1a3009;
}

/* Footer Styles */
.cricket-footer {
    background: linear-gradient(135deg, var(--cricket-green) 0%, #1a3009 100%);
    margin-top: auto;
}

.cricket-footer a:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}

.main-content {
    min-height: calc(100vh - 200px);
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navigation hover effects */
.navbar-nav .nav-link:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}

.navbar-brand:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}

/* Footer Styles */
.cricket-footer {
    background: linear-gradient(135deg, var(--cricket-green) 0%, #1a3009 100%);
    margin-top: auto;
}

.cricket-footer a:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}

.main-content {
    min-height: calc(100vh - 200px);
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Navigation hover effects */
.navbar-nav .nav-link:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}

.navbar-brand:hover {
    color: var(--cricket-gold) !important;
    transition: color 0.3s ease;
}
