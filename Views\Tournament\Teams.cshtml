@model List<WebApplication3.Models.TournamentRegistration>
@{
    ViewData["Title"] = "Registered Teams";
}

<div class="container py-5">
    <div class="row">
        <div class="col-12 text-center mb-5">
            <h1 class="display-4 fw-bold">Registered Teams</h1>
            <p class="lead">Meet the teams competing in Classic Cricket Tournament 2024</p>
        </div>
    </div>

    @if (Model != null && Model.Any())
    {
        <div class="row mb-4">
            <div class="col-12">
                <div class="teams-stats bg-light p-4 rounded">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <h3 class="text-primary">@Model.Count</h3>
                            <p class="mb-0">Teams Registered</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-success">@(Model.Sum(t => t.NumberOfPlayers))</h3>
                            <p class="mb-0">Total Players</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-warning">@(Model.GroupBy(t => t.City).Count())</h3>
                            <p class="mb-0">Cities Represented</p>
                        </div>
                        <div class="col-md-3">
                            <h3 class="text-info">@(32 - Model.Count)</h3>
                            <p class="mb-0">Spots Remaining</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            @foreach (var team in Model)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="team-card">
                        <div class="team-header">
                            <div class="team-logo">
                                <i class="fas fa-shield-alt fa-3x text-primary"></i>
                            </div>
                            <div class="team-info">
                                <h5 class="team-name">@team.TeamName</h5>
                                <p class="team-city text-muted">@team.City</p>
                            </div>
                        </div>
                        
                        <div class="team-details">
                            <div class="detail-item">
                                <span class="detail-label">Captain:</span>
                                <span class="detail-value">@team.CaptainName</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Players:</span>
                                <span class="detail-value">@team.NumberOfPlayers</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Registered:</span>
                                <span class="detail-value">@team.RegistrationDate.ToString("dd MMM yyyy")</span>
                            </div>
                        </div>
                        
                        <div class="team-status">
                            @if (team.IsConfirmed)
                            {
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>Confirmed
                                </span>
                            }
                            else
                            {
                                <span class="badge bg-warning">
                                    <i class="fas fa-clock me-1"></i>Pending
                                </span>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>

        @if (Model.Count < 32)
        {
            <div class="row mt-5">
                <div class="col-12 text-center">
                    <div class="registration-cta">
                        <h3 class="mb-3">Still @(32 - Model.Count) spots available!</h3>
                        <p class="lead mb-4">Don't miss your chance to be part of this exciting tournament</p>
                        <a href="@Url.Action("Register", "Tournament")" class="btn btn-primary btn-lg">
                            <i class="fas fa-user-plus me-2"></i>Register Your Team
                        </a>
                    </div>
                </div>
            </div>
        }
    }
    else
    {
        <div class="row">
            <div class="col-12 text-center">
                <div class="no-teams-message">
                    <i class="fas fa-users fa-5x text-muted mb-4"></i>
                    <h3 class="mb-3">No Teams Registered Yet</h3>
                    <p class="lead mb-4">Be the first team to register for the Classic Cricket Tournament 2024!</p>
                    <a href="@Url.Action("Register", "Tournament")" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus me-2"></i>Register Your Team
                    </a>
                </div>
            </div>
        </div>
    }

    <div class="row mt-5">
        <div class="col-12">
            <div class="tournament-groups">
                <h3 class="text-center mb-4">Tournament Groups</h3>
                <p class="text-center text-muted mb-4">Teams will be divided into groups once registration closes</p>
                
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="group-card">
                            <h5 class="text-center text-primary">Group A</h5>
                            <div class="group-slots">
                                <p class="text-center text-muted">8 Teams</p>
                                <div class="slots-visual">
                                    @for (int i = 1; i <= 8; i++)
                                    {
                                        <div class="slot @(i <= (Model?.Count ?? 0) && i <= 8 ? "filled" : "empty")"></div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="group-card">
                            <h5 class="text-center text-success">Group B</h5>
                            <div class="group-slots">
                                <p class="text-center text-muted">8 Teams</p>
                                <div class="slots-visual">
                                    @for (int i = 9; i <= 16; i++)
                                    {
                                        <div class="slot @(i <= (Model?.Count ?? 0) ? "filled" : "empty")"></div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="group-card">
                            <h5 class="text-center text-warning">Group C</h5>
                            <div class="group-slots">
                                <p class="text-center text-muted">8 Teams</p>
                                <div class="slots-visual">
                                    @for (int i = 17; i <= 24; i++)
                                    {
                                        <div class="slot @(i <= (Model?.Count ?? 0) ? "filled" : "empty")"></div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="group-card">
                            <h5 class="text-center text-danger">Group D</h5>
                            <div class="group-slots">
                                <p class="text-center text-muted">8 Teams</p>
                                <div class="slots-visual">
                                    @for (int i = 25; i <= 32; i++)
                                    {
                                        <div class="slot @(i <= (Model?.Count ?? 0) ? "filled" : "empty")"></div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.team-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    padding: 1.5rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.team-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.team-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.team-logo {
    margin-right: 1rem;
}

.team-name {
    margin-bottom: 0.25rem;
    font-weight: bold;
    color: var(--cricket-green);
}

.team-city {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.team-details {
    margin-bottom: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.detail-label {
    font-weight: 600;
    color: #6c757d;
}

.detail-value {
    color: #495057;
}

.team-status {
    text-align: center;
}

.group-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.slots-visual {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-top: 1rem;
}

.slot {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin: 0 auto;
}

.slot.filled {
    background: var(--cricket-green);
}

.slot.empty {
    background: #e9ecef;
    border: 2px dashed #ced4da;
}

.registration-cta, .no-teams-message {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 15px;
}
</style>
