using System.ComponentModel.DataAnnotations;

namespace WebApplication3.Models
{
    public class ErrorViewModel
    {
        public string? RequestId { get; set; }

        public bool ShowRequestId => !string.IsNullOrEmpty(RequestId);
    }

    public class Team
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string TeamName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string CaptainName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string ContactEmail { get; set; } = string.Empty;

        [Required]
        [Phone]
        public string ContactPhone { get; set; } = string.Empty;

        [StringLength(200)]
        public string City { get; set; } = string.Empty;

        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        public List<Player> Players { get; set; } = new List<Player>();
    }

    public class Player
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string PlayerName { get; set; } = string.Empty;

        [Range(16, 50)]
        public int Age { get; set; }

        [Required]
        [StringLength(50)]
        public string Position { get; set; } = string.Empty; // <PERSON><PERSON>, Bowler, All-rounder, Wicket-keeper

        public int TeamId { get; set; }
        public Team? Team { get; set; }
    }

    public class TournamentRegistration
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string TeamName { get; set; } = string.Empty;

        [Required]
        [StringLength(100)]
        public string CaptainName { get; set; } = string.Empty;

        [Required]
        [EmailAddress]
        public string ContactEmail { get; set; } = string.Empty;

        [Required]
        [Phone]
        public string ContactPhone { get; set; } = string.Empty;

        [Required]
        [StringLength(200)]
        public string City { get; set; } = string.Empty;

        [Range(11, 15)]
        public int NumberOfPlayers { get; set; }

        [StringLength(500)]
        public string AdditionalNotes { get; set; } = string.Empty;

        public DateTime RegistrationDate { get; set; } = DateTime.Now;

        public bool IsConfirmed { get; set; } = false;
    }
}
