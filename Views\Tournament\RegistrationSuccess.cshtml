@model WebApplication3.Models.TournamentRegistration
@{
    ViewData["Title"] = "Registration Successful";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="success-card text-center">
                <div class="success-icon mb-4">
                    <i class="fas fa-check-circle fa-5x text-success"></i>
                </div>
                
                <h1 class="display-4 text-success mb-3">Registration Successful!</h1>
                <p class="lead mb-4">Congratulations! Your team has been successfully registered for the Classic Cricket Tournament 2024.</p>
                
                <div class="registration-details bg-light p-4 rounded mb-4">
                    <h4 class="mb-3">Registration Details</h4>
                    <div class="row text-start">
                        <div class="col-md-6">
                            <p><strong>Registration ID:</strong> <EMAIL>("D4")</p>
                            <p><strong>Team Name:</strong> @Model.TeamName</p>
                            <p><strong>Captain:</strong> @Model.CaptainName</p>
                            <p><strong>City:</strong> @Model.City</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Contact Email:</strong> @Model.ContactEmail</p>
                            <p><strong>Contact Phone:</strong> @Model.ContactPhone</p>
                            <p><strong>Number of Players:</strong> @Model.NumberOfPlayers</p>
                            <p><strong>Registration Date:</strong> @Model.RegistrationDate.ToString("dd MMM yyyy")</p>
                        </div>
                    </div>
                </div>

                <div class="next-steps mb-4">
                    <h4 class="mb-3">What's Next?</h4>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">1</div>
                                <h6>Confirmation Email</h6>
                                <p class="small text-muted">You'll receive a confirmation email within 24 hours</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">2</div>
                                <h6>Payment</h6>
                                <p class="small text-muted">Complete the registration fee payment of ₹5,000</p>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="step-card">
                                <div class="step-number">3</div>
                                <h6>Tournament Schedule</h6>
                                <p class="small text-muted">Receive detailed schedule and venue information</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="important-info alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Important Information</h6>
                    <ul class="list-unstyled mb-0 text-start">
                        <li>• Please save your Registration ID: <strong><EMAIL>("D4")</strong></li>
                        <li>• Check your email regularly for tournament updates</li>
                        <li>• Ensure all players have valid ID proofs ready</li>
                        <li>• Contact us if you don't receive confirmation within 24 hours</li>
                    </ul>
                </div>

                <div class="action-buttons mt-4">
                    <a href="@Url.Action("Index", "Tournament")" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-home me-2"></i>Back to Home
                    </a>
                    <a href="@Url.Action("Schedule", "Tournament")" class="btn btn-outline-primary btn-lg me-3">
                        <i class="fas fa-calendar me-2"></i>View Schedule
                    </a>
                    <a href="@Url.Action("Contact", "Tournament")" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-phone me-2"></i>Contact Us
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.success-card {
    background: white;
    padding: 3rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.step-card {
    padding: 1.5rem;
    border-radius: 10px;
    background: white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    height: 100%;
}

.step-number {
    width: 40px;
    height: 40px;
    background: #007bff;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin: 0 auto 1rem;
}
</style>
