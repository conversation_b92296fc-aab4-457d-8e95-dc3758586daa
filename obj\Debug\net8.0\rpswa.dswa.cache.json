{"GlobalPropertiesHash": "2PDRW0nMjc0mgVK0KkxYNf9nsN8GF3gLNhE3NP1eNmQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["Z8HuCENlFrCPHVAIyBk2QElz7A76Ba6HGFC/YDpOJUc=", "NoI0eazqTTnQy0sK2VNhWdY9mdug4/F2t6tqbKrdPJ4=", "7owmZCd96tM76gtzIBKt0o7KxwWB8DHcTzjqSiK7kNw=", "HC/w4mqSlG1PS+ZBMb+8GzWVaiQyZ3WrW8TUI70/Evk=", "o+dFA6bfmnzzKKGR8XV1aDCbf7wFMgaZojb0pc4MxMA=", "4W4AkNS21ZbnPAfMGxQYZ4FZkJxQV6mkcTfI0xzNnLA=", "M15GiBnLLKd3ErbaA4Z0R7wB6XCr2JRI+10chx1sltE=", "RFigDfaMc9udj3zqf3EejcEVn1zTT/B9Yb3aHjoCvrY=", "bNeyeYFo9leVEA5gQiZ1UN5t6RlvKehP69gkGshWqz8=", "vuLHnpnfazb2HkKYfoSMN2m31fXEYSQr2S8iz8ZP7kw=", "ZZ0mG4iwYe3V8lO/LpDAGBmjlk6zYpxPSCRMcnv1vAI=", "JkDCs033yDGrFq4H3RcOtdhIQ4JIPdklysOVwbzicm4=", "8JMMuHywHCqdR8270fQY7KaEI5h6hP/ygp9kPG/P7gA=", "7OF9cAb2MrGC6pry2Z1y6ZCkGwpl7T3Ad23/Yq9XFJ0=", "RCApEoxg4l61zWnlZXsk8DflYgx09NaG6qHSlmYaKeo=", "WUNjESRlOgdjDEPkYUE4BO/jdtsXVVDZTceW6X3ArpQ=", "iitXnk+W/ctF2qC802Lhtv/HnZNLoDd9mTnvYXUHUEs=", "KRRUsb01SXWOWG8fEmLXVd9GzU1cNR2hWx8zULPBYAU=", "eLLzdiOcWX/xOr9lzDvvXFdlioLTpgmJpCxETZa+Rxc=", "Mi5B8Ha/PM6NUN2h3dGNPXz5MGuvqA9TQx6JG1aIqHU=", "OuXKnpFp+0DUDV9woRnCR/aRsGlrV1XFdA54vxUdn5I=", "17wUgcu/fgV0XaZIWxDNbRfL/iQf4MttR+X0K89FCh8=", "DVCMI8RQQ+8Ss7KUp9X9ZL91eB1RLKw22A+v+Saq6L4=", "vc6SckXgoJuguBtMIywn+bzUsGG8cJ9MmXcFX31qfBs=", "UqzROZW9eq8Z7BpyG2FLVLvkwt42TIRUa7H3AcLn3IA=", "sgRwRRMleohf8GIFDhaonadFwqJnvUEBp4pjVjrTlio=", "A30NEPN9Y56DZnxQ8XyAE4Q3k4zsIeWFbUvuduQ3NUU=", "PxORpPWg5skx2Fs8N/cKdM5SyzBaYGHFvfLcWpp9HFg=", "grNUTtonHDPA7/yCOzl4pCmWB1sgf4M+2TJkWNvji0I=", "F8GSpRyYOAAFOIRxfBcBTKeK8KC2qS5aaLAYY1fqAcI=", "mN28lqMXz5iP9HfkvZ41rscnbRhSxE0hDk+6t1rtLnA=", "PbzOm9NP54AVDvJm73cSh/vRmYf6p3SJz+AMksNTWug=", "wL1k8pauNcvqMjAkk3RLx6Bd8oy2qnyFF2nH3FUcCNA=", "2HW5yaZd3kELhULq+DD/LKtm29p9hulCuQYXP/qS58k=", "uj7XCN4OAv9WrmQKb1l0waHCMhIZsa3dXfJ6d33gsw0=", "UiQ6SUKlmDlDd/smxxTTyeyoIZpWWsvKxFCuf4PdNUA=", "JiszypXuxVlqL47pRiCgnCmZcT3LspidvifDa/DJbs4=", "Xwb2Bhbec2A8Iy+mjlbdpk4Xlz/yEyON9ecDDoXKPBA=", "2dCZbWyRkenGdH4z4mFxESkUiZ+6timS8QGSgmIKDAA=", "KEVYsukMaAoWDh2PAyYnGwsuBNBSIfnVNKAJw5OmwB0=", "EoJNJVlRop9lpC1qbc5xaaOc8JGm7+LfYf6WKR1UQfk=", "AxseyQK5WQFpjkIrdE+Qa5j7v/dpBbAF7cAuZZlV9qI=", "dxNjxjLVvT7J71LuMMitTfAk3/+DjKM2KRV+eZFWFv4=", "QcDbWJM1LiBl4QNs8DfYMXV2mkcRopd4H1Ku4yHXNrg=", "VePOVuspb1gdQoiJTZL93ouDGlSGGN3nfALNQdqL264=", "XalL+V+1h5zRz9Ahx5pYZY62H/OswNNdXUXLYkvqB6s=", "7VnBxn4LpovUbekwq363geNIub6tyE9+BWdQk6Ev+gk=", "jCloUFVRC9w7onBK9xzM3PPlTCeOv1tKQY3V/CQ0s7c=", "TMCE80ygGy3xkXeq1O1cfhmO+F20tnzIXK4eKvLa8ao=", "IK9WzI8CtE7rRB8rSL1pSAFTbCLKVKhlXMJz0pKJbcI=", "WoEYrXv69zcW3C0uvVo2KnUQWp0gUh3Y1/sx9ebR/hY=", "VDlE6n6cLOJRmyrB+eUKtSDtbyAaYO4I3m9wGhe7Fck=", "NGhoGXTIIKW+YIEu12+jbPLN+6X4klAzqkgozRdDlZQ=", "dCmK7RBxt/8H5ZEzma9QeM8I16FcEUQIVNsqUlIxwFw=", "AuJS48bafntgcwUlhSQDXlFqO+aq5Qka+Qeko0hyHdY=", "e3zQi4UMceaU9nX7vzZR45WT9HmhpgJN5ClagSknN18=", "oPS+dHofgIq8vWQid5fJ6u+uDHu9pUVKu1ASuJKCb40=", "Mi19YUodLrIcuidh7I2aSVb56aMeZgEKeMgijXFhwmA=", "M0yBjGUAJ/pGK8sOCcoT5whptiEm+zJ36P4reAEWz9I=", "7RBOaWRN15AgSB1je2hzGsE6FShnncK5WBnn/OFxTY8=", "IlgLWbD5BqareVhP97ITdtTXF3QNymei2zIjlAh3elg=", "RmwUrFkHrc+Ie4+NNIiastiJsTZ0TRd209W3EWR8FK0=", "DLZbPPpROijdj6BfNt7HvrnzYEzwJUMH92FndV2CIpc=", "xJppWI9Xa7VYyHDBmAE4mJUFoQjQSHhNTaRm56c8ZRE=", "y6hnlOaWK9cmsIlI4Z6aUt/zoRQUFI0wFKU5qIrZntk=", "59DnswVjZJDuVW9M8gcmw9ejro3AvWl6rOIHgt48Yvg=", "jzBuywHtNDbYB4Pmvwpt3UwJ9BYrLc8mdebTv/v2vfQ=", "tGULBmViKMsY3sZ9ROrbWzb08uCz+OcNFUD2iyy+514=", "i23RncOgzUn6IJrp1fZCbK9Ydt6wCZKaJ0Zn22Zl/M8=", "PHnJekvZ5jYm9FzYgjZrp/C+95eXhOnEsFfDkEaU8o8=", "KEZcNodl95YcjhH2bMNeDyZrpvaeO8/NY0xlPjrOFkI=", "CeTaF96YrVziAXRdvvijF6GwahQ4Yx8u8JhRNfEWJlE=", "yrRjOASCbaBNDjBcIQb9S+eIxUXJpx+TQm2EREc/QZ4=", "FZw6/lIM4l2o9al4lbdKGV7UxbSzwwO5wjEZh/+xbU0=", "2WajyyiXwTz7kZ5B5BW6YFETywAo8snqfu9jG/cwEA0=", "FyJGPe7uoeKQXTzAszuFL3Bth4IU4SymCQRy11ykLxk=", "bTOoSeNuHj8KVtts/+WzhpJj/xdvilWjtr1MGXNfTGQ=", "qDeVJvo4N4WrWCbDZ2zEsngTvihniHDMO7sP815OVaU=", "GGE97+zInBGn9g6AV9XwBDAP1BSuaLXikNHLgzuslkE=", "vZm7OM+hD1w9y+KBfu6pg1pQ+rvy+gOqtujXbhJ5iWw=", "TxKqKou2t+HoClEaHN4hxyMQHI+cDPF0DoXJwpyQ8tI="], "CachedAssets": {"Z8HuCENlFrCPHVAIyBk2QElz7A76Ba6HGFC/YDpOJUc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\css\\site.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2tiyv64ts", "Integrity": "pAGv4ietcJNk/EwsQZ5BN9+K4MuNYS2a9wl4Jw+q9D0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 362, "LastWriteTime": "2025-07-30T05:27:13.8638059+00:00"}, "7owmZCd96tM76gtzIBKt0o7KxwWB8DHcTzjqSiK7kNw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\favicon.ico", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-30T05:27:13.9949805+00:00"}, "HC/w4mqSlG1PS+ZBMb+8GzWVaiQyZ3WrW8TUI70/Evk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\js\\site.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-30T05:27:13.8657999+00:00"}, "4W4AkNS21ZbnPAfMGxQYZ4FZkJxQV6mkcTfI0xzNnLA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-07-30T05:27:13.9251172+00:00"}, "M15GiBnLLKd3ErbaA4Z0R7wB6XCr2JRI+10chx1sltE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-07-30T05:27:13.9271135+00:00"}, "RFigDfaMc9udj3zqf3EejcEVn1zTT/B9Yb3aHjoCvrY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-07-30T05:27:13.9281071+00:00"}, "bNeyeYFo9leVEA5gQiZ1UN5t6RlvKehP69gkGshWqz8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-07-30T05:27:13.9291037+00:00"}, "vuLHnpnfazb2HkKYfoSMN2m31fXEYSQr2S8iz8ZP7kw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-07-30T05:27:13.9291037+00:00"}, "ZZ0mG4iwYe3V8lO/LpDAGBmjlk6zYpxPSCRMcnv1vAI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-07-30T05:27:13.9313678+00:00"}, "JkDCs033yDGrFq4H3RcOtdhIQ4JIPdklysOVwbzicm4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-07-30T05:27:13.9313678+00:00"}, "8JMMuHywHCqdR8270fQY7KaEI5h6hP/ygp9kPG/P7gA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-07-30T05:27:13.9323714+00:00"}, "7OF9cAb2MrGC6pry2Z1y6ZCkGwpl7T3Ad23/Yq9XFJ0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-07-30T05:27:13.933368+00:00"}, "RCApEoxg4l61zWnlZXsk8DflYgx09NaG6qHSlmYaKeo=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-07-30T05:27:13.9343645+00:00"}, "WUNjESRlOgdjDEPkYUE4BO/jdtsXVVDZTceW6X3ArpQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-07-30T05:27:13.935361+00:00"}, "iitXnk+W/ctF2qC802Lhtv/HnZNLoDd9mTnvYXUHUEs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-07-30T05:27:13.936358+00:00"}, "KRRUsb01SXWOWG8fEmLXVd9GzU1cNR2hWx8zULPBYAU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-07-30T05:27:13.936358+00:00"}, "eLLzdiOcWX/xOr9lzDvvXFdlioLTpgmJpCxETZa+Rxc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-07-30T05:27:13.9373597+00:00"}, "Mi5B8Ha/PM6NUN2h3dGNPXz5MGuvqA9TQx6JG1aIqHU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-07-30T05:27:13.9383517+00:00"}, "OuXKnpFp+0DUDV9woRnCR/aRsGlrV1XFdA54vxUdn5I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-07-30T05:27:13.9393482+00:00"}, "17wUgcu/fgV0XaZIWxDNbRfL/iQf4MttR+X0K89FCh8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-07-30T05:27:13.9403505+00:00"}, "DVCMI8RQQ+8Ss7KUp9X9ZL91eB1RLKw22A+v+Saq6L4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-07-30T05:27:13.9413422+00:00"}, "vc6SckXgoJuguBtMIywn+bzUsGG8cJ9MmXcFX31qfBs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-07-30T05:27:13.9433347+00:00"}, "UqzROZW9eq8Z7BpyG2FLVLvkwt42TIRUa7H3AcLn3IA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-07-30T05:27:13.944331+00:00"}, "sgRwRRMleohf8GIFDhaonadFwqJnvUEBp4pjVjrTlio=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-07-30T05:27:13.944331+00:00"}, "A30NEPN9Y56DZnxQ8XyAE4Q3k4zsIeWFbUvuduQ3NUU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-07-30T05:27:13.9453279+00:00"}, "PxORpPWg5skx2Fs8N/cKdM5SyzBaYGHFvfLcWpp9HFg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-07-30T05:27:13.9463251+00:00"}, "grNUTtonHDPA7/yCOzl4pCmWB1sgf4M+2TJkWNvji0I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-07-30T05:27:13.9473205+00:00"}, "F8GSpRyYOAAFOIRxfBcBTKeK8KC2qS5aaLAYY1fqAcI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-07-30T05:27:13.9483173+00:00"}, "mN28lqMXz5iP9HfkvZ41rscnbRhSxE0hDk+6t1rtLnA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-07-30T05:27:13.9511269+00:00"}, "PbzOm9NP54AVDvJm73cSh/vRmYf6p3SJz+AMksNTWug=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-07-30T05:27:13.9521238+00:00"}, "wL1k8pauNcvqMjAkk3RLx6Bd8oy2qnyFF2nH3FUcCNA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-07-30T05:27:13.9541169+00:00"}, "2HW5yaZd3kELhULq+DD/LKtm29p9hulCuQYXP/qS58k=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-07-30T05:27:13.9561146+00:00"}, "uj7XCN4OAv9WrmQKb1l0waHCMhIZsa3dXfJ6d33gsw0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-07-30T05:27:13.9581034+00:00"}, "UiQ6SUKlmDlDd/smxxTTyeyoIZpWWsvKxFCuf4PdNUA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-07-30T05:27:13.9601001+00:00"}, "JiszypXuxVlqL47pRiCgnCmZcT3LspidvifDa/DJbs4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-07-30T05:27:13.9620903+00:00"}, "Xwb2Bhbec2A8Iy+mjlbdpk4Xlz/yEyON9ecDDoXKPBA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-07-30T05:27:13.9640838+00:00"}, "2dCZbWyRkenGdH4z4mFxESkUiZ+6timS8QGSgmIKDAA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-07-30T05:27:13.9660781+00:00"}, "KEVYsukMaAoWDh2PAyYnGwsuBNBSIfnVNKAJw5OmwB0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-07-30T05:27:13.9670738+00:00"}, "EoJNJVlRop9lpC1qbc5xaaOc8JGm7+LfYf6WKR1UQfk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-07-30T05:27:13.9690672+00:00"}, "AxseyQK5WQFpjkIrdE+Qa5j7v/dpBbAF7cAuZZlV9qI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-07-30T05:27:13.9710603+00:00"}, "dxNjxjLVvT7J71LuMMitTfAk3/+DjKM2KRV+eZFWFv4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-07-30T05:27:13.972057+00:00"}, "QcDbWJM1LiBl4QNs8DfYMXV2mkcRopd4H1Ku4yHXNrg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-07-30T05:27:13.9740539+00:00"}, "VePOVuspb1gdQoiJTZL93ouDGlSGGN3nfALNQdqL264=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-07-30T05:27:13.978037+00:00"}, "XalL+V+1h5zRz9Ahx5pYZY62H/OswNNdXUXLYkvqB6s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-07-30T05:27:13.9800304+00:00"}, "7VnBxn4LpovUbekwq363geNIub6tyE9+BWdQk6Ev+gk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-07-30T05:27:13.9830203+00:00"}, "jCloUFVRC9w7onBK9xzM3PPlTCeOv1tKQY3V/CQ0s7c=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-07-30T05:27:13.9880037+00:00"}, "TMCE80ygGy3xkXeq1O1cfhmO+F20tnzIXK4eKvLa8ao=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-07-30T05:27:13.9919904+00:00"}, "IK9WzI8CtE7rRB8rSL1pSAFTbCLKVKhlXMJz0pKJbcI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-30T05:27:13.9870072+00:00"}, "WoEYrXv69zcW3C0uvVo2KnUQWp0gUh3Y1/sx9ebR/hY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-30T05:27:13.9969775+00:00"}, "VDlE6n6cLOJRmyrB+eUKtSDtbyAaYO4I3m9wGhe7Fck=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-30T05:27:13.9989672+00:00"}, "NGhoGXTIIKW+YIEu12+jbPLN+6X4klAzqkgozRdDlZQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-30T05:27:13.9999637+00:00"}, "dCmK7RBxt/8H5ZEzma9QeM8I16FcEUQIVNsqUlIxwFw=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-07-30T05:27:13.9211301+00:00"}, "AuJS48bafntgcwUlhSQDXlFqO+aq5Qka+Qeko0hyHdY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-07-30T05:27:13.922127+00:00"}, "e3zQi4UMceaU9nX7vzZR45WT9HmhpgJN5ClagSknN18=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-07-30T05:27:13.9231252+00:00"}, "oPS+dHofgIq8vWQid5fJ6u+uDHu9pUVKu1ASuJKCb40=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-07-30T05:27:13.9241204+00:00"}, "Mi19YUodLrIcuidh7I2aSVb56aMeZgEKeMgijXFhwmA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-30T05:27:13.993987+00:00"}, "M0yBjGUAJ/pGK8sOCcoT5whptiEm+zJ36P4reAEWz9I=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-07-30T05:27:13.9171419+00:00"}, "7RBOaWRN15AgSB1je2hzGsE6FShnncK5WBnn/OFxTY8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-07-30T05:27:13.9181401+00:00"}, "IlgLWbD5BqareVhP97ITdtTXF3QNymei2zIjlAh3elg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-07-30T05:27:13.9191366+00:00"}, "RmwUrFkHrc+Ie4+NNIiastiJsTZ0TRd209W3EWR8FK0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-30T05:27:13.9909938+00:00"}, "NoI0eazqTTnQy0sK2VNhWdY9mdug4/F2t6tqbKrdPJ4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\css\\tournament.css", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "css/tournament#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "adj247x1xj", "Integrity": "cuEwbidXuqmQ+WiJ8s1zIoHD+EAyzTxnVSY9gSFWBAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\tournament.css", "FileLength": 8202, "LastWriteTime": "2025-07-30T05:48:18.7297787+00:00"}, "o+dFA6bfmnzzKKGR8XV1aDCbf7wFMgaZojb0pc4MxMA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\js\\tournament.js", "SourceId": "WebApplication3", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\WebApplication3\\WebApplication3\\wwwroot\\", "BasePath": "_content/WebApplication3", "RelativePath": "js/tournament#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vxyjit4nkr", "Integrity": "ox3EAjGb7xB2RgDz7wv/o7zMk70LFyacmZBt0r5Su1Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\tournament.js", "FileLength": 8869, "LastWriteTime": "2025-07-30T05:49:05.2182882+00:00"}}, "CachedCopyCandidates": {}}