@model WebApplication3.Models.TournamentRegistration
@{
    ViewData["Title"] = "Team Registration - Classic Cricket Tournament";
}

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="registration-card">
                <div class="card-header text-center">
                    <h2 class="mb-0">Team Registration</h2>
                    <p class="text-muted mt-2">Register your team for the Classic Cricket Tournament 2024</p>
                </div>
                <div class="card-body">
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="Register" method="post" class="needs-validation" novalidate>
                        @Html.AntiForgeryToken()
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="TeamName" class="form-label">Team Name *</label>
                                <input asp-for="TeamName" class="form-control" placeholder="Enter your team name" required>
                                <span asp-validation-for="TeamName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="CaptainName" class="form-label">Captain Name *</label>
                                <input asp-for="CaptainName" class="form-control" placeholder="Enter captain's name" required>
                                <span asp-validation-for="CaptainName" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="ContactEmail" class="form-label">Contact Email *</label>
                                <input asp-for="ContactEmail" type="email" class="form-control" placeholder="<EMAIL>" required>
                                <span asp-validation-for="ContactEmail" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="ContactPhone" class="form-label">Contact Phone *</label>
                                <input asp-for="ContactPhone" type="tel" class="form-control" placeholder="+91 9876543210" required>
                                <span asp-validation-for="ContactPhone" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="City" class="form-label">City *</label>
                                <input asp-for="City" class="form-control" placeholder="Enter your city" required>
                                <span asp-validation-for="City" class="text-danger"></span>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label asp-for="NumberOfPlayers" class="form-label">Number of Players *</label>
                                <select asp-for="NumberOfPlayers" class="form-select" required>
                                    <option value="">Select number of players</option>
                                    <option value="11">11 Players</option>
                                    <option value="12">12 Players</option>
                                    <option value="13">13 Players</option>
                                    <option value="14">14 Players</option>
                                    <option value="15">15 Players</option>
                                </select>
                                <span asp-validation-for="NumberOfPlayers" class="text-danger"></span>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label asp-for="AdditionalNotes" class="form-label">Additional Notes</label>
                            <textarea asp-for="AdditionalNotes" class="form-control" rows="4" 
                                      placeholder="Any special requirements or additional information..."></textarea>
                            <span asp-validation-for="AdditionalNotes" class="text-danger"></span>
                        </div>

                        <div class="tournament-rules mb-4">
                            <h5>Tournament Rules & Requirements:</h5>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>All players must be between 16-50 years of age</li>
                                <li><i class="fas fa-check text-success me-2"></i>Team must have minimum 11 and maximum 15 players</li>
                                <li><i class="fas fa-check text-success me-2"></i>Registration fee: ₹5,000 per team</li>
                                <li><i class="fas fa-check text-success me-2"></i>All players must provide valid ID proof</li>
                                <li><i class="fas fa-check text-success me-2"></i>Teams must have proper cricket kit and equipment</li>
                            </ul>
                        </div>

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                            <label class="form-check-label" for="agreeTerms">
                                I agree to the <a href="@Url.Action("Rules", "Tournament")" target="_blank">tournament rules and regulations</a> *
                            </label>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg px-5">
                                <i class="fas fa-paper-plane me-2"></i>Register Team
                            </button>
                            <a href="@Url.Action("Index", "Tournament")" class="btn btn-outline-secondary btn-lg px-5 ms-3">
                                Cancel
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
