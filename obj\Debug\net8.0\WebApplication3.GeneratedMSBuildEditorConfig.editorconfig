is_global = true
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = WebApplication3
build_property.RootNamespace = WebApplication3
build_property.ProjectDir = C:\Users\<USER>\source\repos\WebApplication3\WebApplication3\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = 
build_property.MSBuildProjectDirectory = C:\Users\<USER>\source\repos\WebApplication3\WebApplication3
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Home/Privacy.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxQcml2YWN5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/About.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxBYm91dC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/Contact.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxDb250YWN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/Register.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxSZWdpc3Rlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/RegistrationSuccess.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxSZWdpc3RyYXRpb25TdWNjZXNzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Tournament/Schedule.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVG91cm5hbWVudFxTY2hlZHVsZS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[C:/Users/<USER>/source/repos/WebApplication3/WebApplication3/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = b-7kk3sr3rte
