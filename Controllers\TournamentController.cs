using Microsoft.AspNetCore.Mvc;
using WebApplication3.Models;

namespace WebApplication3.Controllers
{
    public class TournamentController : Controller
    {
        private readonly ILogger<TournamentController> _logger;
        private static List<TournamentRegistration> _registrations = new List<TournamentRegistration>();

        public TournamentController(ILogger<TournamentController> logger)
        {
            _logger = logger;
        }

        public IActionResult Index()
        {
            ViewBag.TotalRegistrations = _registrations.Count;
            return View();
        }

        public IActionResult About()
        {
            return View();
        }

        public IActionResult Register()
        {
            return View(new TournamentRegistration());
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult Register(TournamentRegistration registration)
        {
            if (ModelState.IsValid)
            {
                registration.Id = _registrations.Count + 1;
                registration.RegistrationDate = DateTime.Now;
                _registrations.Add(registration);
                
                TempData["SuccessMessage"] = $"Registration successful! Team '{registration.TeamName}' has been registered for the Classic Cricket Tournament.";
                return RedirectToAction("RegistrationSuccess", new { id = registration.Id });
            }
            
            return View(registration);
        }

        public IActionResult RegistrationSuccess(int id)
        {
            var registration = _registrations.FirstOrDefault(r => r.Id == id);
            if (registration == null)
            {
                return NotFound();
            }
            
            return View(registration);
        }

        public IActionResult Teams()
        {
            return View(_registrations.Where(r => r.IsConfirmed).ToList());
        }

        public IActionResult Schedule()
        {
            return View();
        }

        public IActionResult Rules()
        {
            return View();
        }

        public IActionResult Contact()
        {
            return View();
        }

        // Admin action to confirm registrations (simplified)
        [HttpPost]
        public IActionResult ConfirmRegistration(int id)
        {
            var registration = _registrations.FirstOrDefault(r => r.Id == id);
            if (registration != null)
            {
                registration.IsConfirmed = true;
            }
            return RedirectToAction("Teams");
        }
    }
}
